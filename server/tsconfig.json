{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "moduleResolution": "bundler"}, "include": ["**/*"], "exclude": ["node_modules", "dist"]}