import express, { Request, Response } from "express";
import jwt from "jsonwebtoken";
import User from "../models/User";
import { authenticateToken, AuthRequest } from "../middleware/auth";

const router = express.Router();

// Generate JWT token
const generateToken = (userId: string): string => {
  const jwtSecret = process.env.JWT_SECRET!;
  if (!jwtSecret) {
    throw new Error("JWT_SECRET environment variable is not set");
  }
  return jwt.sign({ userId }, jwtSecret, { expiresIn: "7d" });
};

// Sign up
router.post("/signup", async (req: Request, res: Response) => {
  try {
    const { email, password, name } = req.body;

    // Validation
    if (!email || !password || !name) {
      return res.status(400).json({ message: "Alle felter er påkrævet" });
    }

    if (password.length < 6) {
      return res
        .status(400)
        .json({ message: "Adgangskode skal være mindst 6 tegn lang" });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(400)
        .json({ message: "Bruger findes allerede med denne email" });
    }

    // Create new user
    const user = new User({ email, password, name });
    await user.save();

    // Generate token
    const token = generateToken(user._id as string);

    res.status(201).json({
      message: "Bruger oprettet succesfuldt",
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
      },
    });
  } catch (error) {
    console.error("Signup error:", error);
    res.status(500).json({ message: "Serverfejl under oprettelse" });
  }
});

// Sign in
router.post("/signin", async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email og adgangskode er påkrævet" });
    }

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res
        .status(401)
        .json({ message: "Ugyldig email eller adgangskode" });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res
        .status(401)
        .json({ message: "Ugyldig email eller adgangskode" });
    }

    // Generate token
    const token = generateToken(user._id as string);

    res.json({
      message: "Signed in successfully",
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
      },
    });
  } catch (error) {
    console.error("Signin error:", error);
    res.status(500).json({ message: "Server error during signin" });
  }
});

// Get current user (protected route)
router.get(
  "/me",
  authenticateToken,
  async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "User not found" });
      }

      res.json({
        user: {
          id: req.user._id,
          email: req.user.email,
          name: req.user.name,
        },
      });
    } catch (error) {
      console.error("Get user error:", error);
      res.status(500).json({ message: "Server error" });
    }
  },
);

export default router;
