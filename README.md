# Faseplan Backend API

Backend API for the Faseplan work planning application built with Express.js, TypeScript, and MongoDB.

## Features

- RESTful API for task management
- User authentication with JWT
- MongoDB integration with Mongoose
- CORS configuration for frontend integration

## Tech Stack

- **Backend**: Express.js, TypeScript, MongoDB, Mongoose
- **Authentication**: JWT, bcryptjs
- **Development**: Nodemon, tsx

## Local Development

### Prerequisites

- Node.js 18+ 
- MongoDB (local or cloud)

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment files:
   ```bash
   cp .env.example server/.env
   ```

4. Update `server/.env` with your configuration:
   ```
   PORT=3001
   MONGODB_URI=your_mongodb_connection_string
   CLIENT_URL=http://localhost:5173
   JWT_SECRET=your_jwt_secret
   ```

5. Start backend development server:
   ```bash
   npm run server:local
   ```

   Or start both frontend and backend:
   ```bash
   npm run dev:full
   ```

## Backend API Deployment on Render

### Option 1: Using render.yaml (Recommended)

1. Push your code to GitHub
2. Connect your repository to Render
3. Render will automatically detect the `render.yaml` file
4. Set the following environment variables in Render dashboard:
   - `MONGODB_URI`: Your MongoDB connection string
   - `CLIENT_URL`: Your frontend URL (e.g., https://yourfrontend.com)
   - `JWT_SECRET`: A secure random string

### Option 2: Manual Configuration

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Configure the service:
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm start`
   - **Node Version**: 18+

4. Set environment variables:
   - `NODE_ENV=production`
   - `MONGODB_URI=your_mongodb_connection_string`
   - `CLIENT_URL=your_frontend_url`
   - `JWT_SECRET=your_secure_jwt_secret`

### API Endpoints

Once deployed, your API will be available at `https://your-service-name.onrender.com` with the following endpoints:

- `GET /api/health` - Health check endpoint
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- Additional endpoints as defined in your routes

### Important Notes

- The build process compiles TypeScript to JavaScript in the `server/dist` folder
- Render will automatically run the build process during deployment
- Make sure to set all required environment variables in the Render dashboard
- The server will automatically use Render's assigned PORT environment variable

## Scripts

- `npm run dev` - Start frontend development server (for local testing)
- `npm run server:local` - Start backend development server
- `npm run dev:full` - Start both frontend and backend
- `npm run build` - Build backend for production
- `npm start` - Start production server
- `npm run server:build` - Build backend only
- `npm run server:start` - Start backend only

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port (default: 3001) | No |
| `MONGODB_URI` | MongoDB connection string | Yes |
| `CLIENT_URL` | Frontend URL for CORS | Yes |
| `JWT_SECRET` | Secret for JWT token signing | Yes |
| `NODE_ENV` | Environment (development/production) | No |
