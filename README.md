# Faseplan - Work Planning Application

A full-stack work planning application built with React, TypeScript, Express, and MongoDB.

## Features

- Calendar-based task planning
- User authentication
- Dark/light mode
- Responsive design

## Tech Stack

- **Frontend**: React 19, <PERSON><PERSON>, Vite, Tailwind CSS
- **Backend**: Express.js, TypeScript, MongoDB, Mongoose
- **Authentication**: JWT, bcryptjs

## Local Development

### Prerequisites

- Node.js 18+ 
- MongoDB (local or cloud)

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment files:
   ```bash
   cp .env.example server/.env
   ```

4. Update `server/.env` with your configuration:
   ```
   PORT=3001
   MONGODB_URI=your_mongodb_connection_string
   CLIENT_URL=http://localhost:5173
   JWT_SECRET=your_jwt_secret
   ```

5. Start development servers:
   ```bash
   npm run dev:full
   ```

## Deployment on Render

### Option 1: Using render.yaml (Recommended)

1. Push your code to GitHub
2. Connect your repository to Render
3. <PERSON><PERSON> will automatically detect the `render.yaml` file
4. Set the following environment variables in Render dashboard:
   - `MONGODB_URI`: Your MongoDB connection string
   - `CLIENT_URL`: Your frontend URL (e.g., https://yourapp.onrender.com)
   - `JWT_SECRET`: A secure random string

### Option 2: Manual Configuration

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Configure the service:
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm start`
   - **Node Version**: 18+

4. Set environment variables:
   - `NODE_ENV=production`
   - `MONGODB_URI=your_mongodb_connection_string`
   - `CLIENT_URL=your_frontend_url`
   - `JWT_SECRET=your_secure_jwt_secret`

## Scripts

- `npm run dev` - Start frontend development server
- `npm run server:local` - Start backend development server
- `npm run dev:full` - Start both frontend and backend
- `npm run build` - Build both frontend and backend for production
- `npm start` - Start production server
- `npm run server:build` - Build backend only
- `npm run server:start` - Start backend only

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port (default: 3001) | No |
| `MONGODB_URI` | MongoDB connection string | Yes |
| `CLIENT_URL` | Frontend URL for CORS | Yes |
| `JWT_SECRET` | Secret for JWT token signing | Yes |
| `NODE_ENV` | Environment (development/production) | No |
