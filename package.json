{"name": "faseplan", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server:local": "nodemon", "server:build": "tsc --project server/tsconfig.json", "server:start": "node server/dist/index.js", "dev:full": "concurrently \"npm run server:local\" \"npm run dev\""}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "mongoose": "^8.17.0", "react": "^19.0.0", "react-dom": "^19.0.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.1.0", "tailwindcss": "^3.2.7", "typescript": "~5.7.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.2.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "nodemon": "^3.1.10", "postcss": "^8.5.3", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}