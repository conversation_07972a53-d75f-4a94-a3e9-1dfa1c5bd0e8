import { useState, useEffect } from 'react';
import { DayData } from '../types';
import DayEditor from './DayEditor';

interface DayProps {
  date: Date;
  dayData?: DayData[];
  onSave: (data: DayData) => void;
  onDelete?: (date: Date, taskId: string) => void;
  darkMode?: boolean;
  isWeekend?: boolean;
  isCurrentMonth?: boolean;
}

const Day: React.FC<DayProps> = ({ date, dayData = [], onSave, onDelete, darkMode = false, isWeekend = false, isCurrentMonth = true }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingTask, setEditingTask] = useState<DayData | null>(null);
  const [showTaskSelector, setShowTaskSelector] = useState(false);

  const formattedDate = date.getDate();
  const dayOfWeek = date.toLocaleDateString('da-DK', { weekday: 'short' });

  const handleClick = (e: React.MouseEvent) => {
    // Get the target element
    const target = e.target as HTMLElement;

    // Check if the click is on a button or inside a button
    if (target.tagName === 'BUTTON' ||
        target.closest('button') ||
        target.classList.contains('more-tasks-button') ||
        target.closest('.more-tasks-button')) {
      // Don't do anything if clicking on a button
      return;
    }

    if (dayData.length > 0) {
      // If there are existing tasks, open the task selector or edit the single task
      if (dayData.length === 1) {
        // If there's only one task, open it directly
        setEditingTask(dayData[0]);
        setIsEditing(true);
      } else {
        // If there are multiple tasks, show the task selector
        setShowTaskSelector(true);
      }
    } else {
      // Create a new task when there are no existing tasks
      setEditingTask(null);
      setIsEditing(true);
    }
  };

  const handleTaskClick = (e: React.MouseEvent, task: DayData) => {
    e.stopPropagation(); // Prevent triggering the day click handler
    setEditingTask(task);
    setIsEditing(true);
  };

  const handleSave = (data: DayData) => {
    onSave(data);
    setIsEditing(false);
    setEditingTask(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingTask(null);
  };

  const handleDelete = (taskId: string) => {
    if (onDelete) {
      onDelete(date, taskId);
    }
  };

  // Close task selector when clicking outside
  useEffect(() => {
    if (showTaskSelector) {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as HTMLElement;

        // Check if the click is on the overlay (outside the selector)
        if (target.classList.contains('task-selector-overlay')) {
          setShowTaskSelector(false);
        }
      };

      // Use mousedown to catch clicks before they bubble up
      document.addEventListener('mousedown', handleClickOutside);

      // Also add escape key handler
      const handleEscKey = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          setShowTaskSelector(false);
        }
      };

      document.addEventListener('keydown', handleEscKey);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscKey);
      };
    }
  }, [showTaskSelector, date]);

  return (
    <>
      <div
        onClick={handleClick}
        className={`
          h-28 p-2 border cursor-pointer transition-all
          hover:shadow-lg flex flex-col relative
          ${darkMode ? 'border-gray-700' : 'border-gray-300'}
          ${isWeekend ? (darkMode ? 'bg-gray-800/50' : 'bg-gray-100/70') : ''}
          ${!isCurrentMonth ? 'opacity-60' : ''}
          group rounded-lg
          ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}
          ${darkMode ? 'bg-gray-800' : 'bg-white'}
        `}
      >
        {/* Date indicator */}
        <div className="flex justify-between items-start mb-2">
          <div className={`flex items-center justify-center rounded-full w-8 h-8 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <span className={`font-bold text-lg transition-colors duration-200 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{formattedDate}</span>
          </div>
          <span className={`text-xs font-medium transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-500'} px-1.5 py-0.5 rounded-md ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>{dayOfWeek}</span>
        </div>

        {/* Tasks */}
        <div className="flex-1">
          {dayData.length > 0 ? (
            <div className="mb-1 space-y-1">
              {/* Show tasks with full width when there are 1-2 tasks */}
              <div className={`${dayData.length === 1 ? 'space-y-1' : 'grid grid-cols-2 gap-1'}`}>
                {dayData.slice(0, 2).map((task) => (
                  <div
                    key={task.id}
                    onClick={(e) => handleTaskClick(e, task)}
                    className={`
                      p-1 rounded cursor-pointer transition-all
                      hover:shadow text-xs relative
                      ${dayData.length === 1 ? 'col-span-2' : ''}
                    `}
                    style={{
                      backgroundColor: task.color || (darkMode ? '#1f2937' : 'white'),
                      color: task.color ? 'white' : (darkMode ? '#e5e7eb' : '#1f2937')
                    }}
                  >
                    <div className="font-medium truncate">{task.name}</div>
                  </div>
                ))}
              </div>

              {/* Show "+X opgaver" button if there are more than 2 tasks */}
              {dayData.length > 2 && (
                <button
                  type="button" // Explicitly set as button to prevent form submission
                  onClick={(e) => {
                    e.preventDefault(); // Prevent default behavior
                    e.stopPropagation(); // Stop event from bubbling up

                    // Show the task selector
                    setShowTaskSelector(true);

                    // Prevent any other handlers from executing
                    return false;
                  }}
                  className={`
                    w-full py-1 px-2 rounded cursor-pointer transition-all text-center
                    hover:shadow text-xs font-medium mt-1 more-tasks-button
                    ${darkMode
                      ? 'bg-indigo-600 text-white hover:bg-indigo-500'
                      : 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'}
                  `}
                >
                  +{dayData.length - 2} opgaver
                </button>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              {/* Empty state - no plus icon */}
            </div>
          )}
        </div>

        {/* Hover action buttons */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center bg-black bg-opacity-10">
          <div className="flex space-x-2">
            {/* Add button - always visible */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                setEditingTask(null);
                setIsEditing(true);
              }}
              className={`w-8 h-8 rounded-full flex items-center justify-center ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-md cursor-pointer hover:scale-105 transition-transform duration-200`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${darkMode ? 'text-gray-200' : 'text-gray-700'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>

            {/* Edit button - only visible if there are tasks */}
            {dayData.length > 0 && (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  if (dayData.length === 1) {
                    // If there's only one task, open it directly
                    setEditingTask(dayData[0]);
                    setIsEditing(true);
                  } else {
                    // If there are multiple tasks, show the task selector
                    setShowTaskSelector(true);
                  }
                }}
                className={`w-8 h-8 rounded-full flex items-center justify-center ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-md cursor-pointer hover:scale-105 transition-transform duration-200`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${darkMode ? 'text-gray-200' : 'text-gray-700'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>

      {isEditing && (
        <DayEditor
          date={date}
          dayData={editingTask}
          onSave={handleSave}
          onCancel={handleCancel}
          onDelete={editingTask ? () => handleDelete(editingTask.id) : undefined}
          darkMode={darkMode}
        />
      )}

      {/* Task selector popup */}
      {showTaskSelector && dayData.length > 0 && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 task-selector-overlay">
          <div
            id={`task-selector-${date.toISOString()}`}
            className={`relative w-full max-w-xs rounded-lg shadow-xl overflow-hidden ${darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'}`}
          >

            <div className={`py-2 px-3 font-medium text-sm flex justify-between items-center ${darkMode ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800'}`}>
              <span>Vælg opgave at redigere</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowTaskSelector(false);
                }}
                className={`w-6 h-6 rounded-full flex items-center justify-center ${darkMode ? 'hover:bg-gray-600 text-gray-300' : 'hover:bg-gray-200 text-gray-500'}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            <div className="py-1 max-h-48 overflow-y-auto">
              {dayData.map(task => (
                <button
                  key={task.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingTask(task);
                    setIsEditing(true);
                    setShowTaskSelector(false);
                  }}
                  className={`flex items-center w-full text-left px-3 py-2 text-sm ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'}`}
                >
                  <div
                    className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                    style={{ backgroundColor: task.color || '#4f46e5' }}
                  />
                  <div className="truncate">{task.name}</div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Day;
