import React, { useState } from 'react';
import SignIn from './SignIn';
import SignUp from './SignUp';

interface AuthPageProps {
  darkMode: boolean;
}

const AuthPage: React.FC<AuthPageProps> = ({ darkMode }) => {
  const [isSignUp, setIsSignUp] = useState(false);

  return (
    <div className={`min-h-screen flex items-center justify-center px-4 transition-colors duration-200 ${
      darkMode ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="w-full max-w-md">
        {/* Logo/Header */}
        <div className="text-center mb-8">
          <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Faseplan
          </h1>
          <p className={`mt-2 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Arbejdsplanlægger
          </p>
        </div>

        {/* Auth Form */}
        {isSignUp ? (
          <SignUp 
            darkMode={darkMode} 
            onSwitchToSignIn={() => setIsSignUp(false)} 
          />
        ) : (
          <SignIn 
            darkMode={darkMode} 
            onSwitchToSignUp={() => setIsSignUp(true)} 
          />
        )}
      </div>
    </div>
  );
};

export default AuthPage;
